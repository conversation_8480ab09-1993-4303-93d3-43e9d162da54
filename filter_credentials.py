#!/usr/bin/env python3
"""
Script to remove database credentials from config.yaml in Git history
using git-filter-repo
"""

import re


def filter_config_yaml(blob, metadata):
    """
    Filter function to replace database credentials in config.yaml
    """
    # Only process config.yaml files
    if metadata.filename != b'config.yaml':
        return blob

    # Convert blob to string for processing
    content = blob.data.decode('utf-8', errors='ignore')

    # Replace database credentials with placeholders
    # Pattern to match the database section
    database_pattern = r'(database:\s*\n(?:\s+[^:]+:[^\n]*\n)*)'

    replacement = """database:
  name: "your_database_name"
  user: "your_username"
  password: "your_password"
  host: "your_host"
  port: "your_port"
"""

    # Replace the database section
    new_content = re.sub(database_pattern, replacement,
                         content, flags=re.MULTILINE)

    # Also handle individual credential lines if the above pattern doesn't match
    patterns_to_replace = [
        (r'(\s+password:\s*")[^"]*(")', r'\1your_password\2'),
        (r'(\s+user:\s*")[^"]*(")', r'\1your_username\2'),
        (r'(\s+name:\s*")[^"]*(")', r'\1your_database_name\2'),
        (r'(\s+host:\s*")[^"]*(")', r'\1your_host\2'),
        (r'(\s+port:\s*")[^"]*(")', r'\1your_port\2'),
    ]

    for pattern, replacement_text in patterns_to_replace:
        new_content = re.sub(pattern, replacement_text, new_content)

    # Update the blob data
    blob.data = new_content.encode('utf-8')
    return blob


# This script will be used with git-filter-repo
if __name__ == "__main__":
    print("This script is designed to be used with git-filter-repo")
    print("Usage: git filter-repo --blob-callback 'python filter_credentials.py'")

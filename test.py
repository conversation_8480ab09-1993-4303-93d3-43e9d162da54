# from langchain_google_genai import ChatGoogleGenerativeAI

# # Replace with your actual API key
# import os
# # apikey = "AIzaSyClDKiG4-Mahe90xmasszY1gLL9R1X_HKc"
# apikey= "AIzaSyBx5U9Z1Mflcr6i4NKDe5ZkoGXsefcK6f4"
# llm = ChatGoogleGenerativeAI(
#     model="gemini-2.0-flash", temperature=0.2, api_key=apikey)

# response = llm.invoke(
#     "Give a one-sentence summary of the transformer architecture.")
# print(response)


import anthropic

client = anthropic.Anthropic()

models = list(client.models.list(limit=20))
for model in models:
    print(model)
